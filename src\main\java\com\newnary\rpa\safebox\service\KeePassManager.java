package com.newnary.rpa.safebox.service;

import org.linguafranca.pwdb.kdbx.KdbxHeader;
import org.linguafranca.pwdb.kdbx.KdbxStreamFormat;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonSerializableDatabase;

import java.io.IOException;

/**
 * KeePass管理
 *
 * <AUTHOR>
 * @since Created on 2025-09-09
 **/
public class KeePassManager {

    public JacksonDatabase createEmptyDatabase() {
        // 使用V4默认参数创建空白数据库, AES, Argon2-d, ChaCha20
        KdbxStreamFormat streamFormat = new KdbxStreamFormat(new KdbxHeader(4));
        try {
            return new JacksonDatabase(JacksonSerializableDatabase.createEmptyDatabase(), streamFormat);
        } catch (IOException e) {
            // 永不发生
            throw new RuntimeException(e);
        }
    }


}
